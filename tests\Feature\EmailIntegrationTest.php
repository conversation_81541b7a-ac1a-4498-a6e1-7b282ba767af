<?php

namespace Tests\Feature;

use App\Mail\JobApplicationReceived;
use App\Mail\OrderConfirmation;
use App\Mail\PaymentReceived;
use App\Mail\ProjectApplicationReceived;
use App\Models\Job;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Service;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EmailIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $product;
    protected $cart;

    protected function setUp(): void
    {
        parent::setUp();
        
        Mail::fake();
        Storage::fake('public');
        
        $this->user = User::factory()->create();
        
        $category = ProductCategory::factory()->create(['is_active' => true]);
        $this->product = Product::factory()->create([
            'price' => 100.00,
            'is_active' => true,
            'track_inventory' => false,
        ]);
        $this->product->categories()->attach($category);
        
        $this->cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'subtotal' => 100.00,
            'tax_amount' => 15.00,
            'total' => 115.00,
        ]);
        
        $this->cart->items()->create([
            'product_id' => $this->product->id,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
        ]);
    }

    /** @test */
    public function checkout_process_sends_order_confirmation_email()
    {
        // Ensure default currency exists
        $currency = \App\Models\Currency::firstOrCreate(
            ['code' => 'ZAR'],
            [
                'name' => 'South African Rand',
                'symbol' => 'R',
                'exchange_rate' => 1.0,
                'is_default' => true,
                'is_active' => true,
            ]
        );

        $checkoutData = [
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '+27123456789',
            'billing_address_line_1' => '123 Main St',
            'billing_address_line_2' => '',
            'billing_city' => 'Cape Town',
            'billing_state' => 'Western Cape',
            'billing_postal_code' => '8001',
            'billing_country' => 'ZA',
            'ship_to_different_address' => false,
            'payment_method' => 'stripe',
            'terms_accepted' => true,
        ];

        $response = $this->actingAs($this->user)
            ->post('/checkout/process', $checkoutData);

        // Check if the request was successful
        $response->assertRedirect();

        Mail::assertQueued(OrderConfirmation::class, function ($mail) {
            return $mail->order->email === '<EMAIL>';
        });
    }

    /** @test */
    public function payment_processing_sends_payment_received_email()
    {
        // Get or create the default currency
        $currency = \App\Models\Currency::firstOrCreate(
            ['code' => 'ZAR'],
            [
                'name' => 'South African Rand',
                'symbol' => 'R',
                'exchange_rate' => 1.0,
                'is_default' => true,
                'is_active' => true,
            ]
        );

        $order = \App\Models\Order::factory()->create([
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'payment_status' => 'pending',
            'total_amount' => 115.00,
            'currency_id' => $currency->id,
        ]);

        // Create a payment record directly instead of calling Stripe
        $payment = \App\Models\Payment::create([
            'order_id' => $order->id,
            'payment_method' => 'stripe',
            'payment_gateway' => 'stripe',
            'transaction_id' => 'ch_test_123456',
            'amount' => $order->total_amount,
            'currency_id' => $currency->id,
            'status' => 'completed',
            'gateway_response' => json_encode(['id' => 'ch_test_123456']),
            'processed_at' => now(),
        ]);

        // Update order status
        $order->update([
            'payment_status' => 'paid',
            'status' => 'processing',
        ]);

        // Send payment received email
        $emailService = new \App\Services\EmailNotificationService();
        $emailService->sendPaymentReceived($order, $payment);

        Mail::assertQueued(\App\Mail\PaymentReceived::class, function ($mail) use ($order) {
            return $mail->order->id === $order->id;
        });
    }

    /** @test */
    public function job_application_submission_sends_confirmation_email()
    {
        $job = Job::factory()->create([
            'title' => 'Senior Developer',
            'is_active' => true,
        ]);

        $applicationData = [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '+27987654321',
            'country' => 'South Africa',
            'cover_letter' => 'I am excited to apply for this position...',
            'experience_summary' => '5 years of development experience',
            'skills' => ['PHP', 'Laravel', 'JavaScript'],
            'highest_qualification' => 'Degree',
            'terms_accepted' => true,
        ];

        $response = $this->post("/careers/job/{$job->slug}/apply", $applicationData);

        $response->assertStatus(302); // Should redirect after successful submission

        // Check if job application was created
        $this->assertDatabaseHas('job_applications', [
            'email' => '<EMAIL>',
            'first_name' => 'Jane',
            'last_name' => 'Smith',
        ]);

        Mail::assertQueued(JobApplicationReceived::class, function ($mail) {
            return $mail->application->email === '<EMAIL>';
        });
    }

    /** @test */
    public function project_application_submission_sends_confirmation_email()
    {
        $service = Service::factory()->create(['is_active' => true]);

        $applicationData = [
            'service_id' => $service->id,
            'full_name' => 'Bob Johnson',
            'email' => '<EMAIL>',
            'phone' => '+***********',
            'company' => 'Tech Corp',
            'country' => 'South Africa',
            'preferred_contact_method' => 'email',
            'title' => 'Website Development Project',
            'project_type' => 'Website Development',
            'description' => 'We need a modern website...',
            'budget_range' => '$5,000 - $10,000',
            'timeline' => '2-3 months',
            'requirements' => 'Responsive design, CMS integration',
            'priority' => 'medium',
            'terms_accepted' => true,
        ];

        $response = $this->post('/apply', $applicationData);

        $response->assertStatus(302); // Should redirect after successful submission

        Mail::assertQueued(ProjectApplicationReceived::class, function ($mail) {
            return $mail->application->email === '<EMAIL>';
        });
    }

    /** @test */
    public function guest_checkout_sends_order_confirmation_email()
    {
        // Ensure default currency exists
        $currency = \App\Models\Currency::firstOrCreate(
            ['code' => 'ZAR'],
            [
                'name' => 'South African Rand',
                'symbol' => 'R',
                'exchange_rate' => 1.0,
                'is_default' => true,
                'is_active' => true,
            ]
        );

        // Start a session and get the session ID
        $this->startSession();
        $sessionId = session()->getId();

        // Use the same cart setup as the working test, but without authentication
        // This reuses the cart created in setUp() but removes the user association
        $this->cart->update(['user_id' => null, 'session_id' => $sessionId]);

        $checkoutData = [
            'email' => '<EMAIL>',
            'first_name' => 'Guest',
            'last_name' => 'User',
            'phone' => '+***********',
            'billing_address_line_1' => '456 Oak Ave',
            'billing_address_line_2' => '',
            'billing_city' => 'Johannesburg',
            'billing_state' => 'Gauteng',
            'billing_postal_code' => '2000',
            'billing_country' => 'ZA',
            'ship_to_different_address' => false,
            'payment_method' => 'stripe',
            'terms_accepted' => true,
            'create_account' => false,
        ];

        // Make the request with the same session
        $response = $this->post('/checkout/process', $checkoutData);

        // Check if the request was successful
        $response->assertRedirect();

        Mail::assertQueued(OrderConfirmation::class, function ($mail) {
            return $mail->order->email === '<EMAIL>' &&
                   $mail->order->user_id === null;
        });
    }

    /** @test */
    public function job_application_with_attachments_sends_email()
    {
        $job = Job::factory()->create(['is_active' => true]);

        $applicationData = [
            'first_name' => 'Alice',
            'last_name' => 'Wilson',
            'email' => '<EMAIL>',
            'phone' => '+***********',
            'country' => 'South Africa',
            'cover_letter' => 'Please find my application attached...',
            'experience_summary' => 'Experienced developer with strong skills',
            'highest_qualification' => 'Degree',
            'current_position' => 'Software Developer',
            'current_company' => 'Tech Corp',
            'notice_period' => '1 month',
            'terms_accepted' => true,
        ];

        $response = $this->post("/careers/job/{$job->slug}/apply", $applicationData);

        // Check for validation errors or other issues
        if ($response->status() === 302) {
            $session = $response->getSession();
            if ($session && $session->has('errors')) {
                $errors = $session->get('errors');
                $this->fail('Request failed with validation errors: ' . json_encode($errors->all()));
            }
        }

        // Check if the request was successful
        $response->assertRedirect();

        // Check if the job application was created
        $this->assertDatabaseHas('job_applications', [
            'email' => '<EMAIL>',
            'job_id' => $job->id,
        ]);

        Mail::assertQueued(JobApplicationReceived::class, function ($mail) {
            return $mail->application->email === '<EMAIL>';
        });
    }

    /** @test */
    public function project_application_with_multiple_files_sends_email()
    {
        $service = Service::factory()->create(['is_active' => true]);

        $applicationData = [
            'service_id' => $service->id,
            'full_name' => 'Carol Davis',
            'email' => '<EMAIL>',
            'phone' => '+27555666777',
            'company' => 'Design Studio',
            'country' => 'South Africa',
            'preferred_contact_method' => 'email',
            'title' => 'Website Design Project',
            'project_type' => 'Website Development',
            'description' => 'Detailed project requirements attached...',
            'budget_range' => '$5,000 - $10,000',
            'timeline' => '2-3 months',
            'requirements' => 'Responsive design, CMS integration',
            'priority' => 'medium',
            'terms_accepted' => true,
        ];

        $response = $this->post('/apply', $applicationData);

        // Check for validation errors or other issues
        if ($response->status() === 302) {
            $session = $response->getSession();
            if ($session && $session->has('errors')) {
                $errors = $session->get('errors');
                $this->fail('Request failed with validation errors: ' . json_encode($errors->all()));
            }
        }

        // Check if the request was successful
        $response->assertRedirect();

        // Check if the project application was created
        $this->assertDatabaseHas('project_applications', [
            'email' => '<EMAIL>',
            'service_id' => $service->id,
        ]);

        Mail::assertQueued(ProjectApplicationReceived::class, function ($mail) {
            return $mail->application->email === '<EMAIL>';
        });
    }

    /** @test */
    public function authenticated_user_applications_send_emails()
    {
        $job = Job::factory()->create(['is_active' => true]);

        $applicationData = [
            'first_name' => $this->user->name,
            'last_name' => 'User',
            'email' => $this->user->email,
            'phone' => '+27123456789',
            'country' => 'South Africa',
            'cover_letter' => 'I am interested in this position...',
            'experience_summary' => '3 years experience',
            'current_position' => 'Software Developer',
            'current_company' => 'Tech Corp',
            'notice_period' => '1 month',
            'skills' => ['Laravel', 'Vue.js', 'MySQL'],
            'highest_qualification' => 'Degree',
            'terms_accepted' => true,
        ];

        $this->actingAs($this->user)
            ->post("/careers/job/{$job->slug}/apply", $applicationData);

        Mail::assertQueued(JobApplicationReceived::class, function ($mail) {
            return $mail->application->email === $this->user->email &&
                   $mail->application->user_id === $this->user->id;
        });
    }

    /** @test */
    public function order_with_coupon_shows_discount_in_email()
    {
        // Ensure default currency exists
        $currency = \App\Models\Currency::firstOrCreate(
            ['code' => 'ZAR'],
            [
                'name' => 'South African Rand',
                'symbol' => 'R',
                'exchange_rate' => 1.0,
                'is_default' => true,
                'is_active' => true,
            ]
        );

        $coupon = \App\Models\Coupon::factory()->create([
            'code' => 'SAVE10',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
        ]);

        $this->cart->update([
            'coupon_id' => $coupon->uuid,
            'coupon_code' => $coupon->code,
            'discount_amount' => 10.00,
            'total' => 105.00,
        ]);

        $checkoutData = [
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '+27123456789',
            'billing_address_line_1' => '123 Main St',
            'billing_address_line_2' => '',
            'billing_city' => 'Cape Town',
            'billing_state' => 'Western Cape',
            'billing_postal_code' => '8001',
            'billing_country' => 'ZA',
            'ship_to_different_address' => false,
            'payment_method' => 'stripe',
            'terms_accepted' => true,
        ];

        $response = $this->actingAs($this->user)
            ->post('/checkout/process', $checkoutData);

        // Check if the request was successful
        $response->assertRedirect();

        Mail::assertQueued(OrderConfirmation::class, function ($mail) {
            return $mail->order->coupon_code === 'SAVE10' &&
                   $mail->order->discount_amount == 10.00;
        });
    }


}
